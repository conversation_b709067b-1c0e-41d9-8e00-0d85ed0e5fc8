import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:agora_rtm/agora_rtm.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_user.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_rtc_service.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';

abstract class IAudioRoomService {
  void setupRtcEventHandlers({
    UserJoinedCallback? onUserJoined,
    ConnectionStateChangedCallback? onConnectionStateChanged,
    UserOfflineCallback? onUserOffline,
    AudioVolumeCallback? onAudioVolumeIndication,
    JoinChannelSuccessCallback? onJoinChannelSuccess,
    LeaveChannelCallback? onLeaveChannel,
    UserMuteAudioCallback? onUserMuteAudio,
    ErrorCallback? onError,
    PermissionErrorCallback? onPermissionError,
    EncryptionErrorCallback? onEncryptionError,
    ClientRoleChangedCallback? onClientRoleChanged,
  });

  void setupRtmEventHandlers({
    void Function(MessageEvent event)? onMessageEvent,
    void Function(PresenceEvent event)? onPresenceEvent,
    void Function(StorageEvent event)? onStorageEvent,
    void Function(TopicEvent event)? onTopicEvent,
    void Function(TokenEvent event)? onTokenPrivilegeWillExpire,
  });

  Future<VoidResult> joinChannel({
    required String channelId,
    required String token,
    required int uid,
    Future<VoidResult> Function()? afterLogin,
  });

  Future<ResultWithData<List<RoomUser>>> fetchChannelMembers();

  Future<ResultWithData<List<MetadataItem>>> fetchChannelMetadata();

  Future<VoidResult> removeChannelMetadata(List<String>? keys);

  Future<VoidResult> setChannelMultiMetadata(List<MetadataItem> items);

  Future<VoidResult> updateChannelMetadata(List<MetadataItem> items);

  Future<VoidResult> setUserMetadata(RoomUser user);

  Future<VoidResult> setClientRole(ClientRoleType role);

  Future<VoidResult> muteLocalAudio(bool mute);

  Future<VoidResult> muteAllRemoteAudio(bool muted);

  Future<VoidResult> muteRemoteAudio(int uid, bool muted);

  Future<VoidResult> sendChannelMessage(RoomMessageModel message);

  Future<VoidResult> sendMessageToUser(String uid, RoomMessageModel message);

  RoomUser? convertMetadataToUser(List<StateItem> metadata);

  Map<String, String> convertUserToMetadata(RoomUser user);

  Future<VoidResult> renewToken();

  Future<void> leaveChannel();

  Future<VoidResult> dispose();

  // ========== 聊天相关方法 ==========

  /// 初始化聊天服务
  Future<VoidResult> initializeChatService();

  /// 发送文字聊天消息
  Future<VoidResult> sendChatMessage({
    required String content,
    required int senderId,
    required String senderName,
    String? senderAvatar,
    String? roomId,
  });

  /// 获取聊天消息流
  Stream<ChatMessageModel>? get chatMessageStream;

  /// 获取文字消息流
  Stream<ChatMessageModel>? get textMessageStream;

  /// 获取系统消息流
  Stream<ChatMessageModel>? get systemMessageStream;

  /// 添加系统消息
  void addSystemMessage(String content, {String? roomId});

  /// 处理房间消息并转换为聊天消息
  void handleRoomMessageForChat(RoomMessageModel roomMessage);
}
