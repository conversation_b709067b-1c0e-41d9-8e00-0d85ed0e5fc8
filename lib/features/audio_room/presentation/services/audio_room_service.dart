import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:agora_rtm/agora_rtm.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_user.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_audio_room_service.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/services/chat_service.dart';
import 'package:flutter_audio_room/flavors.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';

import '../../domain/interfaces/i_rtc_service.dart';
import '../../domain/interfaces/i_rtm_service.dart';

class AudioRoomService implements IAudioRoomService {
  final IRtcService _rtcService;
  final IRtmService _rtmService;
  ChatService? _chatService;

  AudioRoomService({
    required IRtcService rtcService,
    required IRtmService rtmService,
  })  : _rtcService = rtcService,
        _rtmService = rtmService;

  @override
  void setupRtcEventHandlers({
    UserJoinedCallback? onUserJoined,
    ConnectionStateChangedCallback? onConnectionStateChanged,
    UserOfflineCallback? onUserOffline,
    AudioVolumeCallback? onAudioVolumeIndication,
    JoinChannelSuccessCallback? onJoinChannelSuccess,
    LeaveChannelCallback? onLeaveChannel,
    UserMuteAudioCallback? onUserMuteAudio,
    ErrorCallback? onError,
    PermissionErrorCallback? onPermissionError,
    EncryptionErrorCallback? onEncryptionError,
    ClientRoleChangedCallback? onClientRoleChanged,
  }) {
    _rtcService
      ..onUserJoined = onUserJoined
      ..onConnectionStateChanged = onConnectionStateChanged
      ..onUserOffline = onUserOffline
      ..onAudioVolumeIndication = onAudioVolumeIndication
      ..onJoinChannelSuccess = onJoinChannelSuccess
      ..onLeaveChannel = onLeaveChannel
      ..onUserMuteAudio = onUserMuteAudio
      ..onError = onError
      ..onEncryptionError = onEncryptionError
      ..onPermissionError = onPermissionError
      ..onClientRoleChanged = onClientRoleChanged;
  }

  @override
  void setupRtmEventHandlers({
    void Function(MessageEvent event)? onMessageEvent,
    void Function(PresenceEvent event)? onPresenceEvent,
    void Function(StorageEvent event)? onStorageEvent,
    void Function(TopicEvent event)? onTopicEvent,
    void Function(TokenEvent event)? onTokenPrivilegeWillExpire,
  }) {
    _rtmService
      ..onMessageEvent = onMessageEvent
      ..onPresenceEvent = onPresenceEvent
      ..onStorageEvent = onStorageEvent
      ..onTopicEvent = onTopicEvent
      ..onTokenPrivilegeWillExpire = onTokenPrivilegeWillExpire;
  }

  @override
  Future<VoidResult> joinChannel({
    required String channelId,
    required String token,
    required int uid,
    Future<VoidResult> Function()? afterLogin,
    EncryptionConfig? config,
  }) async {
    final rtmEncryptionConfig = config == null
        ? null
        : RtmEncryptionConfig(
            encryptionMode: RtmEncryptionMode.aes128Gcm,
            encryptionKey: config.encryptionKey,
            encryptionSalt: config.encryptionKdfSalt,
          );

    final rtmInitResult = await _rtmService.initialize(
      userId: uid.toString(),
      appId: F.agoraAppId,
      encryptionConfig: rtmEncryptionConfig,
    );
    if (rtmInitResult.isLeft()) return rtmInitResult;

    final loginResult = await _rtmService.login(token, channelId);
    if (loginResult.isLeft()) return loginResult;

    if (afterLogin != null) {
      final afterLoginResult = await afterLogin();
      if (afterLoginResult.isLeft()) return afterLoginResult;
    }

    return await _rtcService.joinChannel(
      token: token,
      channelId: channelId,
      uid: uid,
    );
  }

  @override
  Future<ResultWithData<List<RoomUser>>> fetchChannelMembers() async {
    return _rtmService.fetchChannelMembers();
  }

  @override
  Future<ResultWithData<List<MetadataItem>>> fetchChannelMetadata() async {
    return _rtmService.fetchChannelInfo();
  }

  @override
  Future<VoidResult> removeChannelMetadata(List<String>? keys) async {
    return _rtmService.removeChannelMetadata(keys);
  }

  @override
  Future<VoidResult> setChannelMultiMetadata(
      List<MetadataItem> metadata) async {
    return _rtmService.setChannelMultiMetadata(metadata);
  }

  @override
  Future<VoidResult> updateChannelMetadata(List<MetadataItem> metadata) async {
    return _rtmService.updateChannelMetadata(metadata);
  }

  @override
  Future<VoidResult> setUserMetadata(RoomUser user) async {
    return _rtmService.setUserMetadata(user);
  }

  @override
  Future<VoidResult> setClientRole(ClientRoleType role) async {
    return _rtcService.setClientRole(role);
  }

  @override
  Future<VoidResult> muteLocalAudio(bool mute) async {
    return _rtcService.muteLocalAudio(mute);
  }

  @override
  Future<VoidResult> muteAllRemoteAudio(bool muted) async {
    return _rtcService.muteAllRemoteAudio(muted);
  }

  @override
  Future<VoidResult> muteRemoteAudio(int uid, bool muted) async {
    return _rtcService.muteRemoteAudio(uid, muted);
  }

  @override
  Future<VoidResult> sendChannelMessage(
      RoomMessageModel message) async {
    return _rtmService.sendChannelMessage(message);
  }

  @override
  Future<VoidResult> sendMessageToUser(
      String uid, RoomMessageModel message) async {
    return _rtmService.sendMessageToUser(uid, message);
  }

  @override
  RoomUser? convertMetadataToUser(List<StateItem> metadata) {
    return _rtmService.convertMetadataToUser(metadata);
  }

  @override
  Map<String, String> convertUserToMetadata(RoomUser user) {
    return _rtmService.convertUserToMetadata(user);
  }

  @override
  Future<VoidResult> renewToken() async {
    return _rtmService.renewToken();
  }

  @override
  Future<void> leaveChannel() async {
    await _rtcService.leaveChannel();
    await _rtmService.dispose();
  }

  // ========== 聊天相关方法 ==========

  /// 初始化聊天服务
  @override
  Future<VoidResult> initializeChatService() async {
    _chatService = ChatService(_rtmService);
    return await _chatService!.initialize();
  }

  /// 发送文字聊天消息
  @override
  Future<VoidResult> sendChatMessage({
    required String content,
    required int senderId,
    required String senderName,
    String? senderAvatar,
    String? roomId,
  }) async {
    if (_chatService == null) {
      final initResult = await initializeChatService();
      if (initResult.isLeft()) return initResult;
    }

    return await _chatService!.sendTextMessage(
      content: content,
      senderId: senderId,
      senderName: senderName,
      senderAvatar: senderAvatar,
      roomId: roomId,
    );
  }

  /// 获取聊天消息流
  @override
  Stream<ChatMessageModel>? get chatMessageStream =>
      _chatService?.messageStream;

  /// 获取文字消息流
  @override
  Stream<ChatMessageModel>? get textMessageStream =>
      _chatService?.textMessageStream;

  /// 获取系统消息流
  @override
  Stream<ChatMessageModel>? get systemMessageStream =>
      _chatService?.systemMessageStream;

  /// 添加系统消息
  @override
  void addSystemMessage(String content, {String? roomId}) {
    _chatService?.addSystemMessage(content, roomId: roomId);
  }

  /// 处理房间消息并转换为聊天消息
  @override
  void handleRoomMessageForChat(RoomMessageModel roomMessage) {
    _chatService?.handleRoomMessage(roomMessage);
  }

  @override
  Future<VoidResult> dispose() async {
    _chatService?.dispose();
    await _rtmService.dispose();
    await _rtcService.dispose();

    return const Right(null);
  }
}
