import 'dart:convert';
import 'dart:typed_data';

import 'package:agora_rtm/agora_rtm.dart' as rtm;
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_rtm_service.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';

import '../rtm_config.dart';

/// Stream Channel 管理 Mixin
/// 专门处理文字聊天相关的 Stream Channel 操作
mixin RtmStreamChannelMixin on IRtmService {
  rtm.StreamChannel? _streamChannel;
  static const String textChatTopic = 'text_chat';

  /// 获取 Stream Channel 实例
  rtm.StreamChannel? get streamChannel => _streamChannel;

  /// 设置 Stream Channel 实例
  void setStreamChannel(rtm.StreamChannel? channel) => _streamChannel = channel;

  /// 创建并加入 Stream Channel
  @override
  Future<VoidResult> createAndJoinStreamChannel() async {
    return executeRtmOperation(
      operation: 'createAndJoinStreamChannel',
      action: () async {
        if (client == null || channelName == null) {
          return (RtmConfig.defaultErrorStatus, null);
        }

        // 创建 Stream Channel
        final (createStatus, channel) =
            await client!.createStreamChannel(channelName!);
        if (createStatus.error) {
          return (createStatus, null);
        }

        setStreamChannel(channel);

        // 加入 Stream Channel
        final (joinStatus, _) = await channel!.join(
          token: token,
          withPresence: true,
          withMetadata: false,
          withLock: false,
        );
        if (joinStatus.error) {
          return (joinStatus, null);
        }

        // 加入文字聊天 Topic
        final (topicStatus, _) = await channel.joinTopic(
          textChatTopic,
          qos: rtm.RtmMessageQos.ordered,
        );
        return (topicStatus, null);
      },
    );
  }

  /// 发送文字聊天消息
  @override
  Future<VoidResult> sendChatMessage(ChatMessageModel message) async {
    return executeRtmOperation(
      operation: 'sendChatMessage',
      action: () async {
        if (_streamChannel == null) {
          return (RtmConfig.defaultErrorStatus, null);
        }

        final messageJson = jsonEncode(message.toJson());
        final messageBytes = Uint8List.fromList(utf8.encode(messageJson));

        // 检查消息大小（Stream Channel 限制 1KB）
        if (messageBytes.length > 1024) {
          return (
            const rtm.RtmStatus(true, '-11010', 'sendChatMessage',
                'Message size exceeds 1KB limit'),
            null
          );
        }

        try {
          final result = await _streamChannel!.publishBinaryMessage(
            textChatTopic,
            messageBytes,
            customType: 'ChatMessage',
          );

          // 处理不同的返回类型
          final (status, _) = result;
          return (status, null);
        } catch (e) {
          return (
            rtm.RtmStatus(true, '-1', 'sendChatMessage', 'Error: $e'),
            null
          );
        }
      },
    );
  }

  /// 离开 Stream Channel
  @override
  Future<VoidResult> leaveStreamChannel() async {
    return executeRtmOperation(
      operation: 'leaveStreamChannel',
      action: () async {
        if (_streamChannel == null) {
          return (
            const rtm.RtmStatus(false, '0', 'leaveStreamChannel', 'Success'),
            null
          );
        }

        // 先离开 Topic
        final (leaveTopicStatus, _) =
            await _streamChannel!.leaveTopic(textChatTopic);
        if (leaveTopicStatus.error) {
          return (leaveTopicStatus, null);
        }

        // 再离开 Channel
        final (leaveChannelStatus, _) = await _streamChannel!.leave();
        if (!leaveChannelStatus.error) {
          setStreamChannel(null);
        }

        return (leaveChannelStatus, null);
      },
    );
  }

  /// 订阅文字聊天 Topic
  @override
  Future<VoidResult> subscribeChatTopic({List<String>? users}) async {
    return executeRtmOperation(
      operation: 'subscribeChatTopic',
      action: () async {
        if (_streamChannel == null) {
          return (RtmConfig.defaultErrorStatus, null);
        }

        final (status, _) = await _streamChannel!.subscribeTopic(
          textChatTopic,
          users: users ?? [],
        );
        return (status, null);
      },
    );
  }

  /// 取消订阅文字聊天 Topic
  @override
  Future<VoidResult> unsubscribeChatTopic({List<String>? users}) async {
    return executeRtmOperation(
      operation: 'unsubscribeChatTopic',
      action: () async {
        if (_streamChannel == null) {
          return (RtmConfig.defaultErrorStatus, null);
        }

        final (status, _) = await _streamChannel!.unsubscribeTopic(
          textChatTopic,
          users: users ?? [],
        );
        return (status, null);
      },
    );
  }
}
