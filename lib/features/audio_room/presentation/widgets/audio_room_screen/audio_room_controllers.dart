import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/tap_user_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/modal/invite_mic_modal.dart';
import 'package:flutter_audio_room/gen/assets.gen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 通用圆形图标按钮组件
class _CircleIconButton extends StatelessWidget {
  /// 创建一个圆形图标按钮
  const _CircleIconButton({
    required this.icon,
    required this.onPressed,
    this.visible = true,
  });

  /// 要显示的图标
  final Widget icon;

  /// 按钮点击回调
  final VoidCallback onPressed;

  /// 按钮是否可见
  final bool visible;

  @override
  Widget build(BuildContext context) {
    if (!visible) return const SizedBox.shrink();

    return Container(
      height: 40.h,
      width: 40.h,
      padding: EdgeInsets.all(1.w),
      margin: EdgeInsets.only(left: 5.w),
      decoration: BoxDecoration(
        color: context.colorScheme.surface.withValues(alpha: 0.3),
        shape: BoxShape.circle,
      ),
      child: GestureDetector(
        onTap: onPressed,
        child: icon,
      ),
    );
  }
}

class AudioRoomControls extends ConsumerStatefulWidget {
  const AudioRoomControls({super.key});

  @override
  ConsumerState<AudioRoomControls> createState() => _AudioRoomControlsState();
}

class _AudioRoomControlsState extends ConsumerState<AudioRoomControls>
    with TapUserMixin {
  @override
  Widget build(context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // _luckyDrawButton(),
        // _chatButton(), // 移除聊天按钮，聊天功能已集成到左侧面板
        _giftButton(),
        _toggleMuteButton(),
        _requestMicButton(),
        _inviteMicButton(),
      ],
    );
  }

  Widget _toggleMuteButton() {
    return Consumer(builder: (context, ref, child) {
      final onMic = ref
          .watch(audioRoomProvider.select((state) => state.currentUserOnMic));
      final isMuted =
          ref.watch(audioRoomProvider.select((state) => state.isMuted));
      
      return _CircleIconButton(
        icon: isMuted ? const Icon(Icons.mic_off) : const Icon(Icons.mic),
        visible: onMic,
        onPressed: () async {
          final res =
              await ref.read(audioRoomProvider.notifier).toggleMuteByLocal();
          res.fold(
            (l) => LoadingUtils.showError(l.message),
            (r) => null,
          );
        },
      );
    });
  }

  Widget _requestMicButton() {
    return Consumer(builder: (context, ref, child) {
      final isCreator =
          ref.watch(audioRoomProvider.select((state) => state.isCreator));
      final onMic = ref
          .watch(audioRoomProvider.select((state) => state.currentUserOnMic));
      
      // 只有非创建者且当前不在麦上的用户才显示请求上麦按钮
      final shouldShow = !isCreator && !onMic;

      return _CircleIconButton(
        icon: const Icon(Icons.mic),
        visible: shouldShow,
        onPressed: () => _handleRequestMic(ref),
      );
    });
  }

  /// 处理请求上麦逻辑
  void _handleRequestMic(WidgetRef ref) {
    final uid = ref.read(audioRoomProvider).currentUid;
    if (uid == null) {
      LoadingUtils.showError('User not found');
      return;
    }
    
    final firstEmptySeat = ref.read(audioRoomProvider).firstEmptySeat();
    if (firstEmptySeat == -1) {
      LoadingUtils.showError('No empty seat found');
      return;
    }

    ref.read(audioRoomProvider.notifier).requestMic(firstEmptySeat);
  }

  // Widget _luckyDrawButton() {
  //   return _CircleIconButton(
  //     icon: Icons.rotate_90_degrees_ccw_sharp,
  //     onPressed: () {
  //       showModalBottomSheet(
  //         context: context,
  //         isScrollControlled: true,
  //         enableDrag: false,
  //         builder: (context) => const LuckyDrawModal(),
  //       );
  //     },
  //   );
  // }



  Widget _giftButton() {
    return _CircleIconButton(
      icon: Assets.images.gift.image(),
      onPressed: () => showGiftModal(null, isAudioRoom: true),
    );
  }

  Widget _inviteMicButton() {
    final isCreatorOrManager = ref.watch(audioRoomProvider.select(
      (state) => state.isCreator || state.isManager,
    ));

    return _CircleIconButton(
      icon: const Icon(Icons.person_add),
      visible: isCreatorOrManager,
      onPressed: () {
        showAdaptiveDialog(
          context: context,
          barrierDismissible: true,
          builder: (context) => const InviteMicModal(),
        );
      },
    );
  }
}
