import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/chat_input_field.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 音频房间聊天输入组件
/// 用于在控制栏中显示聊天输入框
class AudioRoomChatInput extends ConsumerWidget {
  const AudioRoomChatInput({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioRoomState = ref.watch(audioRoomProvider);
    final isChatInitialized =
        ref.watch(audioRoomProvider.notifier).isChatServiceInitialized;

    return Expanded(
      child: Container(
        height: 40.h,
        margin: EdgeInsets.only(right: 8.w),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: ChatInputField(
          onSendMessage: (message) => _handleSendMessage(context, ref, message),
          enabled: isChatInitialized && audioRoomState.currentRoom != null,
          hintText: isChatInitialized ? '说点什么...' : '聊天功能初始化中...',
          maxLength: 200,
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
          showSendButton: false, // 在控制栏中不显示发送按钮，回车发送
        ),
      ),
    );
  }

  /// 处理发送消息
  Future<void> _handleSendMessage(
      BuildContext context, WidgetRef ref, String message) async {
    final audioRoomState = ref.read(audioRoomProvider);
    final isChatInitialized =
        ref.read(audioRoomProvider.notifier).isChatServiceInitialized;

    if (!isChatInitialized) {
      _showError(context, '聊天功能未初始化');
      return;
    }

    final currentUser = audioRoomState.currentUser;
    final currentUid = audioRoomState.currentUid;
    final roomId = audioRoomState.currentRoom?.id.toString();

    if (currentUser == null || currentUid == null) {
      _showError(context, '用户信息获取失败');
      return;
    }

    LogUtils.d('发送聊天消息: $message', tag: 'AudioRoomChatInput');

    // 通过AudioRoomProvider发送聊天消息
    final result = await ref.read(audioRoomProvider.notifier).sendChatMessage(
          content: message,
          roomId: roomId,
        );

    if (result.isLeft()) {
      LogUtils.e(
          '发送消息失败: ${result.fold((l) => l.toString(), (r) => 'Unknown')}',
          tag: 'AudioRoomChatInput');
      _showError(context, '发送消息失败');
    } else {
      LogUtils.d('消息发送成功', tag: 'AudioRoomChatInput');
      // 发送成功，消息会通过流自动添加到UI
    }
  }

  /// 显示错误消息
  void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
