import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_screen/unified_message_list.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/chat_input_field.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 音频房间聊天区域组件
/// 整合了传统房间消息和新的文字聊天功能
class AudioRoomChatSection extends ConsumerStatefulWidget {
  const AudioRoomChatSection({super.key});

  @override
  ConsumerState<AudioRoomChatSection> createState() =>
      _AudioRoomChatSectionState();
}

class _AudioRoomChatSectionState extends ConsumerState<AudioRoomChatSection> {
  @override
  void initState() {
    super.initState();
    LogUtils.d('AudioRoomChatSection初始化', tag: 'AudioRoomChatSection');
  }

  @override
  Widget build(BuildContext context) {
    return _buildUnifiedMessageList();
  }

  /// 构建统一消息列表
  Widget _buildUnifiedMessageList() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(
          AppScreenUtils.setRadius(8),
        ),
      ),
      child: const UnifiedMessageList(
        autoScroll: true,
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  /// 构建聊天输入框
  Widget _buildChatInput() {
    final audioRoomState = ref.watch(audioRoomProvider);
    final isChatInitialized =
        ref.watch(audioRoomProvider.notifier).isChatServiceInitialized;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
      ),
      child: ChatInputField(
        onSendMessage: _handleSendMessage,
        enabled: isChatInitialized && audioRoomState.currentRoom != null,
        hintText: isChatInitialized ? '说点什么...' : '聊天功能初始化中...',
        maxLength: 200,
        padding: EdgeInsets.zero,
      ),
    );
  }

  /// 处理发送消息
  Future<void> _handleSendMessage(String message) async {
    final audioRoomState = ref.read(audioRoomProvider);
    final isChatInitialized =
        ref.read(audioRoomProvider.notifier).isChatServiceInitialized;

    if (!isChatInitialized) {
      _showError('聊天功能未初始化');
      return;
    }

    final currentUser = audioRoomState.currentUser;
    final currentUid = audioRoomState.currentUid;
    final roomId = audioRoomState.currentRoom?.id.toString();

    if (currentUser == null || currentUid == null) {
      _showError('用户信息获取失败');
      return;
    }

    LogUtils.d('发送聊天消息: $message', tag: 'AudioRoomChatSection');

    // 通过AudioRoomProvider发送聊天消息
    final result = await ref.read(audioRoomProvider.notifier).sendChatMessage(
          content: message,
          roomId: roomId,
        );

    if (result.isLeft()) {
      LogUtils.e(
          '发送消息失败: ${result.fold((l) => l.toString(), (r) => 'Unknown')}',
          tag: 'AudioRoomChatSection');
      _showError('发送消息失败');
    } else {
      LogUtils.d('消息发送成功', tag: 'AudioRoomChatSection');
      // 发送成功，消息会通过流自动添加到UI
    }
  }

  /// 显示错误消息
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ),
    );
  }


}
