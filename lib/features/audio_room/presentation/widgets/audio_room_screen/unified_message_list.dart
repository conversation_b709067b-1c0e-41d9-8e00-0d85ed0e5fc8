import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/core/widgets/avatar_with_frame.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_enums.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_message_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/chat_message_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/chat_message_item.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 统一消息列表组件
/// 同时显示房间动态消息和文字聊天消息
class UnifiedMessageList extends ConsumerStatefulWidget {
  final double? height;
  final EdgeInsets? padding;
  final bool autoScroll;

  const UnifiedMessageList({
    super.key,
    this.height,
    this.padding,
    this.autoScroll = true,
  });

  @override
  ConsumerState<UnifiedMessageList> createState() => _UnifiedMessageListState();
}

class _UnifiedMessageListState extends ConsumerState<UnifiedMessageList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final roomMessages = ref.watch(audioRoomMessageProvider);
    final chatMessages = ref.watch(chatMessageProvider);

    // 合并并排序所有消息
    final allMessages = _mergeAndSortMessages(roomMessages, chatMessages);

    // 监听消息变化，自动滚动到底部
    ref.listen<List<RoomMessageModel>>(audioRoomMessageProvider,
        (previous, next) {
      if (widget.autoScroll &&
          next.isNotEmpty &&
          previous != null &&
          next.length > previous.length) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
    });

    ref.listen<List<ChatMessageModel>>(chatMessageProvider, (previous, next) {
      if (widget.autoScroll &&
          next.isNotEmpty &&
          previous != null &&
          next.length > previous.length) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
    });

    return Container(
      height: widget.height,
      padding: widget.padding ??
          EdgeInsets.symmetric(
            horizontal: AppScreenUtils.setWidth(10),
            vertical: AppScreenUtils.setHeight(8),
          ),
      child: allMessages.isEmpty
          ? _buildEmptyState()
          : ListView.separated(
              controller: _scrollController,
              reverse: true, // 最新消息在底部
              shrinkWrap: true,
              itemCount: allMessages.length,
              separatorBuilder: (_, __) => 8.verticalSpace,
              itemBuilder: (context, index) {
                final message = allMessages[allMessages.length - 1 - index];
                return _buildMessageItem(message);
              },
            ),
    );
  }

  /// 合并并排序所有消息
  List<UnifiedMessage> _mergeAndSortMessages(
    List<RoomMessageModel> roomMessages,
    List<ChatMessageModel> chatMessages,
  ) {
    final List<UnifiedMessage> allMessages = [];

    // 添加房间消息
    for (final roomMessage in roomMessages) {
      allMessages.add(UnifiedMessage.fromRoomMessage(roomMessage));
    }

    // 添加聊天消息
    for (final chatMessage in chatMessages) {
      allMessages.add(UnifiedMessage.fromChatMessage(chatMessage));
    }

    // 按时间戳排序
    allMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    return allMessages;
  }

  /// 构建消息项
  Widget _buildMessageItem(UnifiedMessage message) {
    if (message.isRoomMessage) {
      return _RoomMessageItem(message: message.roomMessage!);
    } else {
      return ChatMessageItem(
        message: message.chatMessage!,
        showAvatar: message.chatMessage!.type == ChatMessageType.text,
        showTimestamp: false, // 统一在这里控制时间戳显示
      );
    }
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 48,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无消息',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '开始聊天吧！',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// 滚动到底部
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }
}

/// 统一消息数据模型
class UnifiedMessage {
  final RoomMessageModel? roomMessage;
  final ChatMessageModel? chatMessage;
  final int timestamp;
  final bool isRoomMessage;

  UnifiedMessage._({
    this.roomMessage,
    this.chatMessage,
    required this.timestamp,
    required this.isRoomMessage,
  });

  factory UnifiedMessage.fromRoomMessage(RoomMessageModel roomMessage) {
    return UnifiedMessage._(
      roomMessage: roomMessage,
      timestamp: roomMessage.createAt ?? DateTime.now().millisecondsSinceEpoch,
      isRoomMessage: true,
    );
  }

  factory UnifiedMessage.fromChatMessage(ChatMessageModel chatMessage) {
    return UnifiedMessage._(
      chatMessage: chatMessage,
      timestamp: chatMessage.timestamp,
      isRoomMessage: false,
    );
  }
}

/// 房间消息项组件（从原RoomMessagesList复制）
class _RoomMessageItem extends StatelessWidget {
  final RoomMessageModel message;

  const _RoomMessageItem({required this.message});

  @override
  Widget build(BuildContext context) {
    return _buildMessageContent(context);
  }

  Widget _buildMessageContent(BuildContext context) {
    // Handle gift messages first
    if (message.event == RoomMessageEvent.giftMessage) {
      return _buildMessageRow(
        context: context,
        messageContent: _buildGiftMessageContent(context),
      );
    } else if (message.event == RoomMessageEvent.followCreator) {
      return _buildMessageRow(
        context: context,
        messageContent: _buildTextSpan(
          context: context,
          actionText: 'followed ${message.targetUser?.firstName ?? 'User'}',
        ),
      );
    } else if (message.event == RoomMessageEvent.followBack) {
      return _buildMessageRow(
        context: context,
        messageContent: _buildTextSpan(
          context: context,
          actionText: 'followed ${message.targetUser?.firstName ?? 'User'}',
        ),
      );
    }

    // Handle other message types based on subEvent
    final subEvent = message.eventSubtype;
    switch (subEvent) {
      case RoomMessageEventSubtype.joinRoom:
        return _buildMessageRow(
          context: context,
          messageContent: _buildTextSpan(
            context: context,
            actionText: 'joined the room',
          ),
          crossAxisAlignment: CrossAxisAlignment.start,
        );
      case RoomMessageEventSubtype.agreeInviteOnMic:
        return _buildMessageRow(
          context: context,
          messageContent: _buildTextSpan(
            context: context,
            actionText: 'is now on the mic',
          ),
        );
      case RoomMessageEventSubtype.dropOnMic:
        return _buildMessageRow(
          context: context,
          messageContent: _buildTextSpan(
            context: context,
            actionText: 'has mic removed',
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  /// Creates a standard message row with avatar and content
  Widget _buildMessageRow({
    required BuildContext context,
    required Widget messageContent,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
  }) {
    final sender = message.sender;
    final avatarUrl = sender?.avatarUrl ?? '';

    return Row(
      crossAxisAlignment: crossAxisAlignment,
      children: [
        AvatarWithFrame(
          avatarUrl: avatarUrl,
          width: 18.w,
          height: 18.w,
        ),
        SizedBox(width: 8.w),
        Expanded(child: messageContent),
      ],
    );
  }

  /// Builds a standard text span with username and action text
  Widget _buildTextSpan({
    required BuildContext context,
    required String actionText,
  }) {
    final username = message.sender?.firstName ?? 'User';

    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: '$username ',
            style: context.theme.textTheme.bodySmall?.copyWith(
              color: context.theme.colorScheme.primary,
            ),
          ),
          TextSpan(text: actionText),
        ],
      ),
      style: context.theme.textTheme.bodySmall,
    );
  }

  /// Builds gift message specific content
  Widget _buildGiftMessageContent(BuildContext context) {
    final username = message.sender?.firstName ?? 'User';
    final gift = message.gift;
    final receivers = message.giftReceives;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: '$username ',
                style: context.theme.textTheme.bodySmall?.copyWith(
                  color: context.theme.colorScheme.primary,
                ),
              ),
              TextSpan(text: 'sent ${gift?.name ?? 'gift'}'),
            ],
          ),
          style: context.theme.textTheme.bodySmall,
        ),
        if (receivers?.isNotEmpty ?? false)
          Text.rich(
            TextSpan(
              text: 'to ',
              children: [
                TextSpan(
                  text: receivers!.join(', '),
                  style: context.theme.textTheme.bodySmall?.copyWith(
                    color: context.theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            style: context.theme.textTheme.bodySmall,
          ),
      ],
    );
  }
}
